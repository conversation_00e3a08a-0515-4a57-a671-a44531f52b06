name: oil_change_tracker
description: A new Flutter project.
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+26

environment:
  sdk: '>=3.2.6 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  intl: ^0.20.2
  go_router: ^13.2.0
  flutter_riverpod: ^2.4.10
  riverpod_annotation: ^2.3.4
  freezed_annotation: ^3.0.0
  json_annotation: ^4.8.1
  firebase_core: ^3.14.0
  firebase_auth: ^5.6.0
  cloud_firestore: ^5.6.9
  google_sign_in: ^6.2.1
  flutter_speed_dial: ^7.0.0
  flutter_secure_storage: ^9.0.0
  app_settings: ^5.1.1
  
  # Firebase
  firebase_storage: ^12.4.7
  firebase_messaging: ^15.2.7
  firebase_app_check: ^0.3.2+7  # Updated to latest compatible version
  firebase_crashlytics: ^4.3.7
  firebase_analytics: ^11.5.0
  firebase_remote_config: ^5.4.5
  
  # Authentication
  google_sign_in_android: ^6.2.1
  
  # State Management
  flutter_hooks: ^0.21.2
  hooks_riverpod: ^2.4.9
  percent_indicator: ^4.2.4
  
  # Local Storage
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  shared_preferences: ^2.2.2
  path_provider: ^2.1.5
  
  # UI Components
  cupertino_icons: ^1.0.2
  google_fonts: ^6.1.0
  fl_chart: ^1.0.0
  flutter_svg: ^2.2.0
  cached_network_image: ^3.4.1
  shimmer: ^3.0.0
  flutter_slidable: ^4.0.0
  settings_ui: ^2.0.2
  
  # Voice Input
  record: ^6.0.0
  flutter_sound: ^9.2.13
  permission_handler: ^12.0.0+1
  
  # Subscription
  in_app_purchase: ^3.1.11
  
  # Utilities
  uuid: ^4.2.1
  url_launcher: ^6.1.14
  package_info_plus: ^8.3.0
  image_picker: ^1.1.2
  connectivity_plus: ^6.1.4
  json_serializable: ^6.7.1
  http: ^1.1.0
  image: ^4.3.0
  permission_handler_platform_interface: ^4.3.0
  flutter_animate: ^4.5.0
  flutter_staggered_animations: ^1.1.1
  smooth_page_indicator: ^1.2.1
  flutter_local_notifications: ^19.3.0
  timeago: ^3.7.0
  geolocator: ^14.0.1
  cloud_functions: ^5.5.2
  logger: ^2.6.0
  timezone: ^0.10.1
  flutter_cache_manager: ^3.4.1
  google_mobile_ads: ^6.0.0
  # webview_flutter: ^4.4.2  # Temporarily disabled due to compatibility issues
  webview_flutter: ^4.13.0  # Updated to latest version
  webview_flutter_android: ^4.7.0  # Updated to correct latest version
  timeline_tile: ^2.0.0
  device_info_plus: ^11.5.0
  rxdart: ^0.27.7
  google_speech: ^4.4.0
  dart_openai: ^5.1.0  # Alternative OpenAI package that's more stable
  gpt_markdown: ^1.1.0  # Modern replacement with LaTeX support

dev_dependencies:
  flutter_test:
    sdk: flutter
  integration_test:
    sdk: flutter
  build_runner: ^2.5.4
  freezed: ^3.0.6
  riverpod_generator: ^2.6.5
  flutter_launcher_icons: ^0.13.1

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml`

# The following section is for asset management
flutter:
  generate: true
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/icons/
    - assets/config.json
    - assets/privacy_policy.html
    - assets/terms_and_conditions.html

  # Add font assets for custom icon fonts
  fonts:
    - family: MaterialIcons
      fonts:
        - asset: assets/fonts/MaterialIcons-Regular.ttf
    - family: Roboto
      fonts:
        - asset: assets/fonts/Roboto-Regular.ttf
        - asset: assets/fonts/Roboto-Medium.ttf
          weight: 500
        - asset: assets/fonts/Roboto-Bold.ttf
          weight: 700
        - asset: assets/fonts/Roboto-Light.ttf
          weight: 300

# Flutter Launcher Icons configuration (must be at root level, not inside the flutter block)
flutter_icons:
  ios: true
  android: true
  image_path_ios: "assets/icons/app_icon.png"
  image_path_android: "assets/icons/app_icon.png"
  remove_alpha_ios: true
  adaptive_icon_background: "#ffffff"
  adaptive_icon_foreground: "assets/icons/app_icon_foreground.png"