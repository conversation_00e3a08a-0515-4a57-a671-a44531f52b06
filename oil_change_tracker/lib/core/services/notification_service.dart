import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/car_model.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz;
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:developer' as dev;
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:oil_change_tracker/generated/app_localizations.dart'; // Import AppLocalizations
import 'package:flutter/foundation.dart' show defaultTargetPlatform; // Import for defaultTargetPlatform

class NotificationService {
  final FlutterLocalNotificationsPlugin _notificationsPlugin;
  static const String _lastScheduledTimeKey = 'last_scheduled_notification_time';
  static const String _fcmTokenKey = 'fcm_token';

  NotificationService(this._notificationsPlugin);

  Future<void> initialize() async {
    tz.initializeTimeZones();

    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    const InitializationSettings initializationSettings =
        InitializationSettings(android: initializationSettingsAndroid);

    await _notificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: (NotificationResponse response) {
        // Handle notification tap
        dev.log('Notification clicked: ${response.payload}');
      },
    );
  }

  Future<bool?> requestPermissions() async {
    if (defaultTargetPlatform == TargetPlatform.android) {
      final AndroidFlutterLocalNotificationsPlugin? androidImplementation =
          _notificationsPlugin.resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>();
      return await androidImplementation?.requestNotificationsPermission();
    } else if (defaultTargetPlatform == TargetPlatform.iOS || defaultTargetPlatform == TargetPlatform.macOS) {
      return await _notificationsPlugin
          .resolvePlatformSpecificImplementation<
              IOSFlutterLocalNotificationsPlugin>()
          ?.requestPermissions(
            alert: true,
            badge: true,
            sound: true,
          );
    }
    return null;
  }

  Future<void> scheduleOilChangeNotification(CarModel car, BuildContext context) async {
    S? l10n;
    try {
      l10n = S.of(context);
    } catch (e) {
      dev.log('Unable to load localizations: $e');
      // Continue with fallback strings
    }
    
    if (car.id == null) {
      dev.log('Cannot schedule notification for car with null ID');
      return;
    }

    try {
      // Cancel existing notifications for this car
      await cancelNotificationsForCar(car.id!);

      // Calculate days until oil change and mileage conditions
      final daysUntilDue = car.daysUntilNextChange;
      final kmUntilDue = car.kilometersUntilNextChange;

      // No need to schedule if already overdue
      if (daysUntilDue <= 0 || kmUntilDue <= 0) {
        await _showOverdueNotification(car, context);
        return;
      }

      // Schedule based on time interval
      await _scheduleTimeBasedNotification(car, daysUntilDue, context);

      // Track that we scheduled notifications
      await _saveLastScheduledTime();

      dev.log('Scheduled oil change notification for car ${car.id}, due in $daysUntilDue days or $kmUntilDue km');
    } catch (e) {
      dev.log('Error in scheduleOilChangeNotification: $e');
      // Swallow error to prevent app crashes, but log it
    }
  }

  Future<void> _showOverdueNotification(CarModel car, BuildContext context) async {
    S? l10n;
    try {
      l10n = S.of(context);
    } catch (e) {
      dev.log('Unable to load localizations: $e');
      // Continue with fallback strings
    }

    // Safety check for null ID
    if (car.id == null) {
      dev.log('Cannot show overdue notification for car with null ID');
      return;
    }

    try {
      final AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
        'oil_change_overdue_${car.id}',
        "Oil Change Overdue!", // Fallback
        channelDescription: 'Notifications for overdue oil changes',
        importance: Importance.high,
        priority: Priority.high,
        color: Colors.red,
      );

      final NotificationDetails notificationDetails =
          NotificationDetails(android: androidDetails);

      // Generate a notification ID based on the car ID's hash code
      final notificationId = car.id!.hashCode & 0x7FFFFFFF;

      await _notificationsPlugin.show(
        notificationId,
        l10n?.oilChangeOverdue ?? "Oil Change Overdue!", // Use l10n if available, fallback otherwise
        '${car.year} ${car.make} ${car.model} is overdue for an oil change', // Fallback
        notificationDetails,
        payload: 'car:${car.id}',
      );
    } catch (e) {
      dev.log('Error in _showOverdueNotification: $e');
      // Swallow error to prevent app crashes, but log it
    }
  }

  Future<void> _scheduleTimeBasedNotification(CarModel car, int daysUntilDue, BuildContext context) async {
    // Safety check for null ID
    if (car.id == null) {
      dev.log('Cannot schedule time-based notification for car with null ID');
      return;
    }

    // Schedule reminders at different intervals
    final List<int> reminderDays = [30, 14, 7, 3, 1];

    for (final days in reminderDays) {
      if (daysUntilDue > days) {
        final scheduledDate = tz.TZDateTime.now(tz.local).add(Duration(days: daysUntilDue - days));

        // Create notification details
        final AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
          'oil_change_reminder_${car.id}_$days',
          "Oil Change Reminder", // Fallback
          channelDescription: 'Scheduled reminders for oil changes',
          importance: Importance.high,
          priority: Priority.high,
          color: Colors.amber,
        );

        final NotificationDetails notificationDetails =
            NotificationDetails(android: androidDetails);

        // Generate a unique notification ID based on car ID and days
        final notificationId = (car.id!.hashCode & 0x7FFFFFFF) + days;

        // Schedule the notification
        await _notificationsPlugin.zonedSchedule(
          notificationId,
          "Oil Change Due Soon", // Fallback
          '${car.year} ${car.make} ${car.model} will need an oil change in $days days', // Fallback
          scheduledDate,
          notificationDetails,
          androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
          payload: 'car:${car.id}',
        );

        dev.log('Scheduled reminder for $days days before due date for car ${car.id}');
      }
    }

    // Schedule the exact due date notification
    final dueDate = tz.TZDateTime.now(tz.local).add(Duration(days: daysUntilDue));
    final AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
      'oil_change_due_${car.id}',
      "Oil Change Due Today", // Fallback
      channelDescription: 'Notifications for due oil changes',
      importance: Importance.high,
      priority: Priority.high,
      color: Colors.amber,
    );

    final NotificationDetails notificationDetails =
        NotificationDetails(android: androidDetails);

    // Generate a notification ID based on the car ID's hash code
    final notificationId = car.id!.hashCode & 0x7FFFFFFF;

    await _notificationsPlugin.zonedSchedule(
      notificationId,
      "Oil Change Due Today", // Fallback
      '${car.year} ${car.make} ${car.model} is due for an oil change today', // Fallback
      dueDate,
      notificationDetails,
      androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
      payload: 'car:${car.id}',
    );
  }

  // --- License Expiry Notifications ---

  Future<void> scheduleLicenseExpiryNotification(CarModel car, BuildContext context) async {
    S? l10n;
    try {
      l10n = S.of(context);
    } catch (e) {
      dev.log('Unable to load localizations: $e');
      // Continue with fallback strings
    }
    
    if (car.id == null || car.licenseExpiryDate == null) {
      dev.log('Cannot schedule license expiry notification for car with null ID or no expiry date');
      return;
    }

    try {
      // Cancel existing license expiry notifications for this car
      await _cancelLicenseExpiryNotificationsForCar(car.id!);

      final daysUntilExpiry = car.daysUntilLicenseExpiry;

      if (daysUntilExpiry <= 0) {
        await _showLicenseExpiredNotification(car, context);
        return;
      }

      await _scheduleLicenseExpiryReminders(car, daysUntilExpiry, context);
      dev.log('Scheduled license expiry notifications for car ${car.id}, expires in $daysUntilExpiry days');
    } catch (e) {
      dev.log('Error in scheduleLicenseExpiryNotification: $e');
      // Swallow error to prevent app crashes, but log it
    }
  }

  Future<void> _showLicenseExpiredNotification(CarModel car, BuildContext context) async {
    S? l10n;
    try {
      l10n = S.of(context);
    } catch (e) {
      dev.log('Unable to load localizations: $e');
      // Continue with fallback strings
    }
    
    // Safety check for null ID
    if (car.id == null) {
      dev.log('Cannot show license expired notification for car with null ID');
      return;
    }

    try {
      final AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
        'license_expired_${car.id}', // Unique channel ID
        "License Expired", // Fallback - removed reference to l10n?.licenseExpired
        channelDescription: 'Notifications for expired licenses',
        importance: Importance.high,
        priority: Priority.high,
        color: Colors.red,
      );

      final NotificationDetails notificationDetails =
          NotificationDetails(android: androidDetails);

      // Use a distinct base ID for license notifications to avoid collision with oil change
      final notificationId = (car.id!.hashCode & 0x7FFFFFFF) + 100000;

      await _notificationsPlugin.show(
        notificationId,
        "License Expired", // Fallback
        '${car.year} ${car.make} ${car.model} license has expired.', // Fallback
        notificationDetails,
        payload: 'license_expiry_car:${car.id}', // Distinct payload
      );
    } catch (e) {
      dev.log('Error in _showLicenseExpiredNotification: $e');
      // Swallow error to prevent app crashes, but log it
    }
  }

  Future<void> _scheduleLicenseExpiryReminders(CarModel car, int daysUntilExpiry, BuildContext context) async {
    // Safety check for null ID
    if (car.id == null) {
      dev.log('Cannot schedule license expiry reminders for car with null ID');
      return;
    }

    try {
      final List<int> reminderDays = [30, 14, 7, 1]; // Reminder intervals

    // Use a distinct base ID for license notifications
    final baseNotificationId = (car.id!.hashCode & 0x7FFFFFFF) + 100000;

    for (final days in reminderDays) {
      if (daysUntilExpiry >= days) {
        final scheduledDate = tz.TZDateTime.now(tz.local).add(Duration(days: daysUntilExpiry - days));

        final AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
          'license_expiry_reminder_${car.id}_$days',
          "License Expiry Reminder", // Fallback
          channelDescription: 'Scheduled reminders for license expiry',
          importance: Importance.high,
          priority: Priority.high,
          color: Colors.orange,
        );

        final NotificationDetails notificationDetails =
            NotificationDetails(android: androidDetails);

        final notificationId = baseNotificationId + days;

        await _notificationsPlugin.zonedSchedule(
          notificationId,
          "License Expiring Soon", // Fallback
          '${car.year} ${car.make} ${car.model} license expires in $days days.', // Fallback
          scheduledDate,
          notificationDetails,
          androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
          payload: 'license_expiry_car:${car.id}',
        );
        dev.log('Scheduled license expiry reminder for $days days before due date for car ${car.id}');
      }
    }

    // Schedule notification for the day of expiry
    final expiryDateScheduled = tz.TZDateTime.now(tz.local).add(Duration(days: daysUntilExpiry));
    final AndroidNotificationDetails onExpiryAndroidDetails = AndroidNotificationDetails(
      'license_expiry_due_${car.id}',
      "License Expires Today!", // Fallback
      channelDescription: 'Notification for license expiring today',
      importance: Importance.high,
      priority: Priority.high,
      color: Colors.deepOrange,
    );
    final NotificationDetails onExpiryNotificationDetails =
        NotificationDetails(android: onExpiryAndroidDetails);

    // Use a distinct ID for the D-day notification, e.g., base + a number not in reminderDays
    final onExpiryNotificationId = baseNotificationId + 31;

    await _notificationsPlugin.zonedSchedule(
      onExpiryNotificationId,
      "License Expires Today!", // Fallback
      '${car.year} ${car.make} ${car.model} license expires today.', // Fallback
      expiryDateScheduled,
      onExpiryNotificationDetails,
      androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
      payload: 'license_expiry_car:${car.id}',
    );
     dev.log('Scheduled license expiry notification for D-Day for car ${car.id}');
    } catch (e) {
      dev.log('Error in _scheduleLicenseExpiryReminders: $e');
      // Swallow error to prevent app crashes, but log it
    }
  }

  Future<void> _cancelLicenseExpiryNotificationsForCar(String carId) async {
    final baseNotificationId = (carId.hashCode & 0x7FFFFFFF) + 100000;
    // Cancel immediate expired notification
    await _notificationsPlugin.cancel(baseNotificationId);
    // Cancel reminder notifications and D-day notification
    final reminderDaysWithDDay = [30, 14, 7, 1, 31];
    for (final dayOffset in reminderDaysWithDDay) {
      await _notificationsPlugin.cancel(baseNotificationId + dayOffset);
    }
     dev.log('Cancelled license expiry notifications for car $carId');
  }

  Future<void> cancelNotificationsForCar(String carId) async {
    // Cancel oil change notifications
    final oilChangeBaseId = carId.hashCode & 0x7FFFFFFF;
    // Cancel overdue notification (base ID)
    await _notificationsPlugin.cancel(oilChangeBaseId);
    // Cancel reminder notifications (base ID + reminder day)
    final oilChangeReminderDays = [30, 14, 7, 3, 1];
    for (int dayOffset in oilChangeReminderDays) {
      await _notificationsPlugin.cancel(oilChangeBaseId + dayOffset);
    }
    dev.log('Cancelled oil change notifications for car $carId');

    // Cancel license expiry notifications
    await _cancelLicenseExpiryNotificationsForCar(carId);
    dev.log('Cancelled ALL notifications for car $carId');
  }

  Future<void> cancelAllNotifications() async {
    await _notificationsPlugin.cancelAll();
  }

  Future<void> _saveLastScheduledTime() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_lastScheduledTimeKey, DateTime.now().millisecondsSinceEpoch);
  }

  Future<DateTime?> getLastScheduledTime() async {
    final prefs = await SharedPreferences.getInstance();
    final timestamp = prefs.getInt(_lastScheduledTimeKey);
    return timestamp != null
        ? DateTime.fromMillisecondsSinceEpoch(timestamp)
        : null;
  }

  Future<void> scheduleNotificationsForAllCars(List<CarModel> cars, BuildContext context) async {
    for (final car in cars) {
      if (car.id != null) {
        await scheduleOilChangeNotification(car, context);
        if (car.licenseExpiryDate != null) {
          await scheduleLicenseExpiryNotification(car, context);
        }
      }
    }
  }

  Future<String?> getAndSaveFCMToken() async {
    try {
      final messaging = FirebaseMessaging.instance;
      final token = await messaging.getToken();

      if (token != null) {
        // Save token to shared preferences
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(_fcmTokenKey, token);
        dev.log('FCM Token saved: ${token.substring(0, 10)}...');
      }

      return token;
    } catch (e) {
      dev.log('Error getting FCM token: $e');
      return null;
    }
  }

  Future<String?> getFCMToken() async {
    try {
      // First try to get from shared preferences
      final prefs = await SharedPreferences.getInstance();
      String? token = prefs.getString(_fcmTokenKey);

      return token;
    } catch (e) {
      dev.log('Error retrieving FCM token: $e');
      return null;
    }
  }
}

final notificationServiceProvider = Provider<NotificationService>((ref) {
  final plugin = FlutterLocalNotificationsPlugin();
  return NotificationService(plugin);
});