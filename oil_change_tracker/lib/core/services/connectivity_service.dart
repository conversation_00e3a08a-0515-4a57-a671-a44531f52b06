import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:async';
import 'dart:developer' as dev;

/// Service to monitor internet connectivity
class ConnectivityService {
  final Connectivity _connectivity = Connectivity();
  late StreamSubscription<List<ConnectivityResult>> _subscription;
  // Initialize _lastResult with a default value to avoid LateInitializationError
  ConnectivityResult _lastResult = ConnectivityResult.none;
  final _connectivityController =
      StreamController<ConnectivityResult>.broadcast();

  /// Stream of connectivity changes that can be listened to
  Stream<ConnectivityResult> get connectivityStream =>
      _connectivityController.stream;

  /// Current connectivity status
  ConnectivityResult get currentStatus => _lastResult;

  /// Whether the device is currently connected to the internet
  bool get isConnected => _lastResult != ConnectivityResult.none;

  /// Initialize the connectivity service and start monitoring
  Future<void> initialize() async {
    try {
      final results = await _connectivity.checkConnectivity();
      _lastResult =
          results.isNotEmpty ? results.first : ConnectivityResult.none;
      _connectivityController.add(_lastResult);

      _subscription = _connectivity.onConnectivityChanged.listen((results) {
        final result =
            results.isNotEmpty ? results.first : ConnectivityResult.none;
        dev.log('ConnectivityService: Connectivity changed to $result');
        _lastResult = result;
        _connectivityController.add(result);
      });

      dev.log('ConnectivityService: Initialized with status: $_lastResult');
    } catch (e) {
      dev.log('ConnectivityService: Error initializing: $e');
      // Default to assuming there is connectivity to avoid blocking the app
      _lastResult = ConnectivityResult.mobile;
    }
  }

  /// Dispose resources
  void dispose() {
    _subscription.cancel();
    _connectivityController.close();
  }
}

/// Provider for the connectivity service
final connectivityServiceProvider = Provider<ConnectivityService>((ref) {
  final service = ConnectivityService();
  // Initialize the service immediately to prevent late initialization
  service.initialize();
  ref.onDispose(() => service.dispose());
  return service;
});

/// Provider for the current connectivity status
final connectivityStatusProvider = StreamProvider<ConnectivityResult>((ref) {
  final service = ref.watch(connectivityServiceProvider);
  return service.connectivityStream;
});

/// Provider to check if the device is connected to the internet
final isConnectedProvider = Provider<bool>((ref) {
  final connectivityStatus = ref.watch(connectivityStatusProvider);
  return connectivityStatus.when(
    data: (status) => status != ConnectivityResult.none,
    loading: () => true, // Assume connected while loading
    error: (_, __) => true, // Assume connected on error
  );
});
