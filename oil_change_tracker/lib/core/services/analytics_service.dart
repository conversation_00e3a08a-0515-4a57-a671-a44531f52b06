import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/car_model.dart';
import '../models/user_model.dart';
import 'package:flutter/foundation.dart' show kDebugMode;
import 'dart:developer' as dev;
import '../../features/subscription/providers/feature_gate_provider.dart';

/// Analytics event types for consistent naming across the app
enum AnalyticsEventType {
  // Car related events
  carAdded,
  carUpdated,
  carDeleted,
  carViewed,

  // Maintenance related events
  oilChangeRecorded,
  maintenanceRecorded,
  maintenanceScheduled,

  // Notification related events
  notificationReceived,
  notificationClicked,
  notificationPermissionChanged,

  // User related events
  userSignIn,
  userSignUp,
  userSignOut,
  profileUpdate,

  // Navigation and screen events
  viewScreen,

  // Feature usage events
  intervalChanged,
  filterChanged,
  imageUploaded,
  settingsChanged,

  // Conversion events
  onboardingCompleted,
  permissionGranted,
  subscriptionPurchased,
}

/// Funnels for tracking multi-step processes
enum AnalyticsFunnel {
  carCreation,
  oilChangeRecord,
  onboarding,
  userRegistration,
}

/// Analytics service that integrates Firebase Analytics with Remote Config
/// and Crashlytics for comprehensive user tracking and experimentation
class AnalyticsService {
  final FirebaseAnalytics _analytics;
  final FirebaseCrashlytics _crashlytics;
  final FirebaseRemoteConfig _remoteConfig;
  final Ref _ref;

  AnalyticsService(
      this._analytics, this._crashlytics, this._remoteConfig, this._ref);

  // Check if enhanced analytics is enabled for the user
  bool get _hasEnhancedAnalytics {
    try {
      return _ref.read(featureGateProvider(PremiumFeature.enhancedAnalytics));
    } catch (e) {
      // If there's an error reading the provider, default to false
      dev.log('Error checking enhanced analytics status: $e');
      return false;
    }
  }

  /// Initialize analytics with appropriate settings
  Future<void> initialize() async {
    try {
      // Set up analytics
      await _initializeAnalytics();

      // Set up remote config
      await _initializeRemoteConfig();

      // Correlate analytics with crashlytics
      _setupCrashlyticsCorrelation();
    } catch (e) {
      dev.log('Error initializing analytics service: $e');
    }
  }

  /// Initialize analytics with appropriate settings
  Future<void> _initializeAnalytics() async {
    try {
      // Only enable analytics in production
      if (!kDebugMode) {
        await _analytics.setAnalyticsCollectionEnabled(true);
      } else {
        // For development, we can still log events but not send them
        await _analytics.setAnalyticsCollectionEnabled(false);
      }

      // Set default event parameters that will be included with all events
      await _analytics.setDefaultEventParameters({
        'app_version': '1.0.1',
        'build_number': '1',
        'platform': 'android',
      });

      dev.log('Analytics successfully initialized');
    } catch (e) {
      dev.log('Error initializing analytics: $e');
      // Don't rethrow - we don't want analytics to crash the app
    }
  }

  /// Initialize Remote Config with default values and fetch settings
  Future<void> _initializeRemoteConfig() async {
    try {
      // Set default values
      await _remoteConfig.setDefaults({
        // Feature flags
        'enable_image_carousel': true,
        'enable_oil_filter_tracking': true,
        'enable_weather_integration': true,
        'enable_dark_mode': true,

        // Timing parameters
        'minimum_fetch_interval_seconds': 3600, // 1 hour
        'oil_change_notification_days_before': 7,

        // UI configurations
        'home_screen_layout': 'enhanced', // or 'classic'
        'max_car_images': 5,

        // Analytics settings
        'analytics_sample_rate': 1.0, // 100% of users
        'verbose_logging': false,

        // AI Usage & Trial Settings
        'ai_trial_days': 7,
        'ai_cap_trial_voice': 75,
        'ai_cap_trial_chat': 150,
        'ai_cap_premium_voice': 75,
        'ai_cap_premium_chat': 150,
      });

      // Configure fetch settings
      await _remoteConfig.setConfigSettings(RemoteConfigSettings(
        fetchTimeout: const Duration(minutes: 1),
        minimumFetchInterval: const Duration(hours: 1),
      ));

      // Fetch and activate
      await _remoteConfig.fetchAndActivate();

      dev.log(
          'Remote Config initialized with active values: ${_remoteConfig.getAll()}');
    } catch (e) {
      dev.log('Error initializing Remote Config: $e');
      // Don't rethrow - we still want the app to work without Remote Config
    }
  }

  /// Set up correlation between analytics and crashlytics
  void _setupCrashlyticsCorrelation() {
    // Set user IDs in Crashlytics when they're set in Analytics
    // We'll use this elsewhere when setting user properties

    // Log the Firebase Analytics collection status to Crashlytics
    try {
      _crashlytics.setCustomKey('analytics_enabled', !kDebugMode);
    } catch (e) {
      dev.log('Error setting up Crashlytics correlation: $e');
    }
  }

  /// Screen tracking with consistent naming
  Future<void> logScreenView({
    required String screenName,
    String? screenClass,
    Map<String, dynamic>? parameters,
  }) async {
    try {
      final params = <String, dynamic>{
        'screen_class': screenClass ?? screenName,
        if (parameters != null) ...parameters,
      };

      dev.log('Logging screen view: $screenName');

      await _analytics.logScreenView(
        screenName: screenName,
        screenClass: screenClass,
        parameters: params.cast<String, Object>(),
      );

      // Also log to crashlytics for correlation
      _crashlytics.setCustomKey('last_screen_viewed', screenName);
    } catch (e) {
      dev.log('Error logging screen view: $e');
    }
  }

  /// Car events - structured for consistency
  Future<void> logCarEvent({
    required AnalyticsEventType eventType,
    required CarModel car,
    Map<String, dynamic>? additionalParams,
  }) async {
    try {
      final String eventName;

      switch (eventType) {
        case AnalyticsEventType.carAdded:
          eventName = 'car_added';
          break;
        case AnalyticsEventType.carUpdated:
          eventName = 'car_updated';
          break;
        case AnalyticsEventType.carDeleted:
          eventName = 'car_deleted';
          break;
        case AnalyticsEventType.carViewed:
          eventName = 'car_viewed';
          break;
        default:
          eventName = 'car_event';
      }

      // Basic parameters for all users
      final Map<String, dynamic> params = {
        'car_id': car.id ?? 'unknown',
        'car_make': car.make,
        'car_model': car.model,
        'car_year': car.year,
      };

      // Enhanced parameters for premium users
      if (_hasEnhancedAnalytics) {
        params.addAll({
          'car_current_mileage': car.currentMileage,
          'car_last_oil_change_mileage': car.lastOilChangeMileage,
          'car_oil_change_interval': car.oilChangeMileageInterval,
          'car_month_interval': car.oilChangeMonthInterval,
          'car_has_images': car.imageUrls != null && car.imageUrls!.isNotEmpty,
          'car_image_count': car.imageUrls?.length ?? 0,
          'car_is_oil_change_due': car.isOilChangeDue,
          'car_kilometers_until_due': car.kilometersUntilNextChange,
          'car_days_until_due': car.daysUntilNextChange,
          'car_oil_change_progress': car.oilChangeProgress,
        });
      }

      // Add any additional parameters
      if (additionalParams != null) {
        params.addAll(additionalParams);
      }

      // Convert boolean values to strings for Firebase Analytics
      final Map<String, dynamic> cleanParams = {};
      params.forEach((key, value) {
        if (value is bool) {
          cleanParams[key] = value.toString();
        } else {
          cleanParams[key] = value;
        }
      });

      await _analytics.logEvent(
        name: eventName,
        parameters: cleanParams.cast<String, Object>(),
      );

      dev.log('Logged car event: $eventName with params: $cleanParams');
    } catch (e) {
      dev.log('Error logging car event: $e');
    }
  }

  /// Maintenance tracking
  Future<void> logMaintenance({
    required String carId,
    required String type,
    required int mileage,
    required double cost,
    String? notes,
    Map<String, dynamic>? additionalParams,
  }) async {
    try {
      dev.log('Logging maintenance: $type for car $carId');

      final params = <String, dynamic>{
        'car_id': carId,
        'maintenance_type': type,
        'mileage': mileage,
        'cost': cost,
        if (notes != null && notes.isNotEmpty) 'has_notes': true,
        if (additionalParams != null) ...additionalParams,
      };

      await _analytics.logEvent(
        name: 'maintenance_recorded',
        parameters: params.cast<String, Object>(),
      );
    } catch (e) {
      dev.log('Error logging maintenance: $e');
    }
  }

  /// Oil change tracking with rich context
  Future<void> logOilChange({
    required String carId,
    required int mileage,
    required int daysSinceLastChange,
    DateTime? lastChangeDate,
    bool hasFilter = false,
    double? oilCost,
    double? filterCost,
    String? oilType,
    Map<String, dynamic>? additionalParams,
  }) async {
    try {
      dev.log('Logging oil change for car $carId');

      final daysOverdue = _calculateDaysOverdue(daysSinceLastChange);
      final overdueCategorical = _categorizeDaysOverdue(daysSinceLastChange);

      final params = <String, dynamic>{
        'car_id': carId,
        'mileage': mileage,
        'days_since_last': daysSinceLastChange,
        'has_filter': hasFilter,
        'days_overdue': daysOverdue > 0 ? daysOverdue : 0,
        'overdue_category': overdueCategorical,
        if (oilCost != null) 'oil_cost': oilCost,
        if (filterCost != null) 'filter_cost': filterCost,
        if (oilType != null) 'oil_type': oilType,
        if (lastChangeDate != null)
          'days_since_car_added':
              DateTime.now().difference(lastChangeDate).inDays,
        if (additionalParams != null) ...additionalParams,
      };

      await _analytics.logEvent(
        name: 'oil_change_recorded',
        parameters: params.cast<String, Object>(),
      );
    } catch (e) {
      dev.log('Error logging oil change: $e');
    }
  }

  /// Calculate how many days an oil change is overdue
  int _calculateDaysOverdue(int daysSinceLastChange) {
    // Get the standard interval from Remote Config
    final standardIntervalDays =
        _remoteConfig.getInt('standard_oil_change_days') > 0
            ? _remoteConfig.getInt('standard_oil_change_days')
            : 180; // Default to 6 months

    return daysSinceLastChange - standardIntervalDays;
  }

  /// Categorize overdue status for analytics segmentation
  String _categorizeDaysOverdue(int daysSinceLastChange) {
    final daysOverdue = _calculateDaysOverdue(daysSinceLastChange);

    if (daysOverdue <= 0) return 'on_time';
    if (daysOverdue <= 30) return 'within_month';
    if (daysOverdue <= 90) return 'within_quarter';
    return 'severely_overdue';
  }

  /// Track user actions with consistent naming
  Future<void> logUserAction({
    required AnalyticsEventType eventType,
    Map<String, dynamic>? parameters,
  }) async {
    try {
      final String eventName;

      switch (eventType) {
        case AnalyticsEventType.userSignIn:
          eventName = 'user_sign_in';
          break;
        case AnalyticsEventType.userSignUp:
          eventName = 'user_sign_up';
          break;
        case AnalyticsEventType.userSignOut:
          eventName = 'user_sign_out';
          break;
        case AnalyticsEventType.profileUpdate:
          eventName = 'profile_updated';
          break;
        case AnalyticsEventType.notificationPermissionChanged:
          eventName = 'notification_permission_changed';
          break;
        case AnalyticsEventType.settingsChanged:
          eventName = 'settings_changed';
          break;
        default:
          eventName = 'user_action';
          break;
      }

      dev.log('Logging user action: $eventName');

      await _analytics.logEvent(
        name: eventName,
        parameters: parameters?.cast<String, Object>(),
      );
    } catch (e) {
      dev.log('Error logging user action: $e');
    }
  }

  /// Record notification interactions
  Future<void> logNotificationInteraction({
    required String notificationType,
    required bool wasClicked,
    String? carId,
    Map<String, dynamic>? additionalParams,
  }) async {
    try {
      dev.log(
          'Logging notification interaction: $notificationType, clicked: $wasClicked');

      final params = <String, dynamic>{
        'notification_type': notificationType,
        'was_clicked': wasClicked,
        if (carId != null) 'car_id': carId,
        if (additionalParams != null) ...additionalParams,
      };

      final eventName =
          wasClicked ? 'notification_clicked' : 'notification_received';

      await _analytics.logEvent(
        name: eventName,
        parameters: params.cast<String, Object>(),
      );
    } catch (e) {
      dev.log('Error logging notification interaction: $e');
    }
  }

  /// Track feature usage for better insight into what users value
  Future<void> trackFeatureUsage({
    required String featureName,
    Map<String, dynamic>? parameters,
  }) async {
    try {
      // Check if the feature is enabled via Remote Config
      final featureFlag = 'enable_$featureName';
      bool isEnabled = true; // Default to enabled

      // Only check RC if the feature has a corresponding flag
      if (_remoteConfig.getAll().containsKey(featureFlag)) {
        isEnabled = _remoteConfig.getBool(featureFlag);
      }

      // Only log if the feature is enabled
      if (isEnabled) {
        dev.log('Logging feature usage: $featureName');

        final params = <String, dynamic>{
          'feature_name': featureName,
        };

        // Convert any boolean values in parameters to strings
        if (parameters != null) {
          parameters.forEach((key, value) {
            if (value is bool) {
              params[key] = value ? "true" : "false";
            } else {
              params[key] = value;
            }
          });
        }

        await _analytics.logEvent(
          name: 'feature_used',
          parameters: params.cast<String, Object>(),
        );
      }
    } catch (e) {
      dev.log('Error tracking feature usage: $e');
    }
  }

  /// Track steps in a multi-step process (funnel)
  Future<void> trackFunnelStep({
    required AnalyticsFunnel funnel,
    required String step,
    required bool isCompleted,
    Map<String, dynamic>? parameters,
  }) async {
    try {
      final String funnelName;

      switch (funnel) {
        case AnalyticsFunnel.carCreation:
          funnelName = 'car_creation_funnel';
          break;
        case AnalyticsFunnel.oilChangeRecord:
          funnelName = 'oil_change_record_funnel';
          break;
        case AnalyticsFunnel.onboarding:
          funnelName = 'onboarding_funnel';
          break;
        case AnalyticsFunnel.userRegistration:
          funnelName = 'user_registration_funnel';
          break;
      }

      final Map<String, dynamic> params = {
        'funnel_name': funnelName,
        'step_name': step,
        'is_completed': isCompleted.toString(),
      };

      // Add enhanced analytics for premium users
      if (_hasEnhancedAnalytics) {
        params['timestamp'] = DateTime.now().toIso8601String();
        params['session_id'] = _getSessionId();

        if (parameters != null) {
          params.addAll(parameters);
        }
      } else if (parameters != null) {
        // For free users, only include basic parameters
        params.addAll(parameters);
      }

      await _analytics.logEvent(
        name: '${funnelName}_${step}',
        parameters: params.cast<String, Object>(),
      );

      dev.log(
          'Tracked funnel step: $funnelName - $step (completed: $isCompleted)');
    } catch (e) {
      dev.log('Error tracking funnel step: $e');
    }
  }

  /// Track a general event
  Future<void> trackEvent({
    required String eventName,
    Map<String, dynamic>? parameters,
  }) async {
    try {
      final Map<String, dynamic> params = parameters ?? {};

      // Add enhanced analytics for premium users
      if (_hasEnhancedAnalytics) {
        params['timestamp'] = DateTime.now().toIso8601String();
        params['session_id'] = _getSessionId();
      }

      await _analytics.logEvent(
        name: eventName,
        parameters: params.cast<String, Object>(),
      );

      dev.log('Tracked event: $eventName');
    } catch (e) {
      dev.log('Error tracking event: $e');
    }
  }

  // Generate a session ID for enhanced analytics
  String _getSessionId() {
    // In a real implementation, this would be stored and maintained across the app session
    return DateTime.now().millisecondsSinceEpoch.toString();
  }

  /// Set user properties for segmentation
  Future<void> setUserProperties({
    required String userId,
    UserModel? user,
    int? carCount,
    bool? hasNotificationsEnabled,
    String? selectedLanguage,
    Map<String, dynamic>? additionalProperties,
  }) async {
    try {
      // Set the user ID for analytics
      await _analytics.setUserId(id: userId);

      // Also set for crashlytics
      await _crashlytics.setUserIdentifier(userId);

      // Set standard properties
      if (carCount != null) {
        await _analytics.setUserProperty(
          name: 'car_count',
          value: carCount.toString(),
        );
        await _crashlytics.setCustomKey('car_count', carCount);
      }

      if (hasNotificationsEnabled != null) {
        await _analytics.setUserProperty(
          name: 'notifications_enabled',
          value: hasNotificationsEnabled.toString(),
        );
      }

      if (selectedLanguage != null) {
        await _analytics.setUserProperty(
          name: 'language',
          value: selectedLanguage,
        );
        await _crashlytics.setCustomKey('language', selectedLanguage);
      }

      // User model specific properties if available
      if (user != null) {
        // Track when user first joined
        if (user.createdAt != null) {
          final daysSinceJoined =
              DateTime.now().difference(user.createdAt!).inDays;
          await _analytics.setUserProperty(
            name: 'days_since_joined',
            value: daysSinceJoined.toString(),
          );
        }

        // Has profile image
        final hasProfileImage =
            user.photoUrl != null && user.photoUrl!.isNotEmpty;
        await _analytics.setUserProperty(
          name: 'has_profile_image',
          value: hasProfileImage.toString(),
        );
      }

      // Add any additional custom properties
      if (additionalProperties != null) {
        for (final entry in additionalProperties.entries) {
          await _analytics.setUserProperty(
            name: entry.key,
            value: entry.value.toString(),
          );
        }
      }

      dev.log('User properties set for user $userId');
    } catch (e) {
      dev.log('Error setting user properties: $e');
    }
  }

  /// Get a Remote Config value with proper type handling
  T getRemoteConfigValue<T>(String key, T defaultValue) {
    try {
      final allValues = _remoteConfig.getAll();
      if (!allValues.containsKey(key)) {
        return defaultValue;
      }

      final value = allValues[key]!.asString();

      if (T == bool) {
        return (value.toLowerCase() == 'true') as T;
      } else if (T == int) {
        return int.tryParse(value) as T? ?? defaultValue;
      } else if (T == double) {
        return double.tryParse(value) as T? ?? defaultValue;
      } else {
        return value as T? ?? defaultValue;
      }
    } catch (e) {
      dev.log('Error getting Remote Config value for $key: $e');
      return defaultValue;
    }
  }

  /// Check if a feature is enabled
  bool isFeatureEnabled(String featureName) {
    final key = 'enable_$featureName';
    return _remoteConfig.getBool(key);
  }

  /// Force a refresh of Remote Config values
  Future<bool> refreshRemoteConfig() async {
    try {
      dev.log('AnalyticsService: Starting Remote Config refresh...');
      // Fetch and activate
      final updated = await _remoteConfig.fetchAndActivate();

      if (updated) {
        dev.log('Remote Config updated with new values');
        // Log all current values for debugging
        final allValues = _remoteConfig.getAll();
        dev.log(
            'AnalyticsService: All Remote Config values: ${allValues.map((key, value) => MapEntry(key, value.asString()))}');
      } else {
        dev.log('Remote Config fetched but no new values found');
        // Still log current values
        final allValues = _remoteConfig.getAll();
        dev.log(
            'AnalyticsService: Current Remote Config values: ${allValues.map((key, value) => MapEntry(key, value.asString()))}');
      }

      return updated;
    } catch (e) {
      dev.log('Error refreshing Remote Config: $e');
      return false;
    }
  }

  /// Generic method to log custom events
  Future<void> logEvent(String eventName,
      [Map<String, dynamic>? parameters]) async {
    try {
      dev.log('Logging custom event: $eventName');

      // Convert boolean values to strings for Firebase Analytics
      final params = <String, dynamic>{};

      if (parameters != null) {
        parameters.forEach((key, value) {
          if (value is bool) {
            params[key] = value ? "true" : "false";
          } else {
            params[key] = value;
          }
        });
      }

      await _analytics.logEvent(
        name: eventName,
        parameters: params.cast<String, Object>(),
      );
    } catch (e) {
      dev.log('Error logging custom event: $e');
    }
  }
}

/// Provider to access the analytics service
final analyticsServiceProvider = Provider<AnalyticsService>((ref) {
  final analytics = FirebaseAnalytics.instance;
  final crashlytics = FirebaseCrashlytics.instance;
  final remoteConfig = FirebaseRemoteConfig.instance;

  final service = AnalyticsService(analytics, crashlytics, remoteConfig, ref);

  // Initialize the service
  service.initialize();

  return service;
});
