import 'package:flutter/material.dart';
import 'package:gpt_markdown/gpt_markdown.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/theme/theme_extensions.dart';
import '../../../../generated/app_localizations.dart';

/// Simple markdown-based subscription & cancellation policy. Adjust copy as needed.
class SubscriptionPolicyScreen extends ConsumerWidget {
  const SubscriptionPolicyScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final s = S.of(context);

    const _md = '''
# Subscription & Cancellation Policy

Thank you for choosing **Oil Change Tracker+**. This document explains how billing, trials, cancellation and refunds work in our app, in compliance with Google Play policy.

---

## 1️⃣ Trial Period
* We offer a **7-day free trial** for first-time subscribers who explicitly opt-in.
* During the trial **no charges** are made and no automatic billing is set up.
* The trial ends automatically after 7 days with no further action required.

## 2️⃣ Billing Cycle
* After the trial ends, you can **choose** to subscribe to continue using premium features.
* Subscriptions are managed through Google Play and follow Google's billing policies.

## 3️⃣ Subscription Management
* All subscriptions are managed through **Google Play > Payments & subscriptions > Subscriptions**.
* You can subscribe, modify, or cancel your subscription through Google Play.
* Premium access continues until the end of any paid period.

## 4️⃣ Refunds
* Google Play handles refunds. To request a refund, visit **payments.google.com** or *Google Play > Order history*, choose the transaction and tap **Refund / Report a problem**.
* We follow Google's standard 48-hour initial refund window. Beyond that we do **not** offer prorated refunds.

## 5️⃣ Price changes
* If we change the price, Google Play will notify you and request consent. If you do not agree, your subscription will be cancelled at the current period's end.

## 6️⃣ Contact
Email **<EMAIL>** with any billing questions.

_Last updated: July 2025_
''';

    return Scaffold(
      backgroundColor: context.containerBackgroundColor,
      appBar: AppBar(
        title: Text(
          s.subscriptionPolicy,
          style: TextStyle(
            color: context.accentColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: context.containerBackgroundColor,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: context.accentColor),
          onPressed: () => context.pop(),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SingleChildScrollView(
          child: GptMarkdown(
            _md,
          ),
        ),
      ),
    );
  }
}
