import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../application/use_cases/record_and_transcribe_use_case.dart';
import '../processors/ai_voice_processor.dart';
import '../models/voice_form_type.dart';
import '../presentation/providers/voice_recording_provider.dart';
import '../presentation/providers/ai_voice_processor_provider.dart';
import '../../../core/utils/logger.dart';
import '../../../core/services/api_key_service.dart' as core_keys;
import 'package:dart_openai/dart_openai.dart';
import '../../auth/providers/auth_provider.dart';
import '../../ai_usage/providers/ai_usage_providers.dart';
// import '../../ai_usage/providers/ai_usage_providers.dart';
// import '../../auth/providers/auth_provider.dart';

/// Voice context for processing commands
class VoiceContext {
  final VoiceFormType originScreen;
  final String languageCode;
  final Map<String, dynamic>? metadata;

  VoiceContext({
    required this.originScreen,
    required this.languageCode,
    this.metadata,
  });
}

/// Result of voice processing
class VoiceResult {
  final bool success;
  final String? transcription;
  final Map<String, dynamic>? extractedData;
  final String? error;

  VoiceResult({
    required this.success,
    this.transcription,
    this.extractedData,
    this.error,
  });
}

/// Unified voice command service
class VoiceCommandService {
  final RecordAndTranscribeUseCase _recordAndTranscribeUseCase;
  final AIVoiceProcessor _aiProcessor;
  final core_keys.ApiKeyService _apiKeyService;
  final Ref _ref;

  String? _currentRecordingId;

  // --- Spell-correction helper ------------------------------------------------
  Future<String> _spellCorrect(String raw) async {
    try {
      final apiKey = _apiKeyService.getOpenAIApiKey();
      if (apiKey.isEmpty) return raw;

      OpenAI.apiKey = apiKey;
      final chatCompletion = await OpenAI.instance.chat.create(
        model: "gpt-3.5-turbo",
        temperature: 0,
        maxTokens: 120,
        messages: [
          OpenAIChatCompletionChoiceMessageModel(
            content: [
              OpenAIChatCompletionChoiceMessageContentItemModel.text(
                'You are a spell checker. Return the text with corrected spelling and spacing only. Do not add or remove words.',
              ),
            ],
            role: OpenAIChatMessageRole.system,
          ),
          OpenAIChatCompletionChoiceMessageModel(
            content: [
              OpenAIChatCompletionChoiceMessageContentItemModel.text(raw),
            ],
            role: OpenAIChatMessageRole.user,
          ),
        ],
      );
      final corrected = chatCompletion.choices.first.message.content?.first.text?.trim();
      return corrected?.isNotEmpty == true ? corrected! : raw;
    } catch (e) {
      // On any failure just return the raw text – never block the flow
      VLOG('spell', 'Spell correction failed: $e');
      return raw;
    }
  }

  VoiceCommandService({
    required RecordAndTranscribeUseCase recordAndTranscribeUseCase,
    required AIVoiceProcessor aiProcessor,
    required core_keys.ApiKeyService apiKeyService,
    required Ref ref,
  })  : _recordAndTranscribeUseCase = recordAndTranscribeUseCase,
        _aiProcessor = aiProcessor,
        _apiKeyService = apiKeyService,
        _ref = ref;

  /// Start recording
  Future<void> startRecording(VoiceContext context) async {
    try {
      final recording = await _recordAndTranscribeUseCase.startRecording(
        languageCode: context.languageCode,
        metadata: context.metadata,
      );
      _currentRecordingId = recording.id;
      VLOG('record',
          'Started recording ${recording.id} for ${context.originScreen}');
    } catch (e) {
      VLOG('record', 'Failed to start recording: $e');
      throw Exception('Failed to start recording: $e');
    }
  }

  /// Stop recording and process the command
  Future<VoiceResult> stopAndProcess(VoiceContext context) async {
    if (_currentRecordingId == null) {
      return VoiceResult(
        success: false,
        error: 'No active recording',
      );
    }

    try {
      // Stop and transcribe using OpenAI Whisper
      VLOG('stt', 'Stopping recording $_currentRecordingId');
      final recording = await _recordAndTranscribeUseCase
          .stopAndTranscribe(_currentRecordingId!);
      _currentRecordingId = null;

      final transcription = recording.transcription;
      if (transcription == null || transcription.isEmpty) {
        VLOG('stt', 'No transcription received');
        return VoiceResult(
          success: false,
          error: 'No speech detected',
        );
      }

      VLOG('stt', 'Transcription: "$transcription"');

      // Optional spell-correction (async but quick ~ few tokens)
      final corrected = await _spellCorrect(transcription);
      VLOG('spell', 'Corrected: "$corrected"');

      // Use AI processor to extract structured data from the transcription
      VLOG('intent', 'Processing with AI for ${context.originScreen}');
      final extractedData = await _aiProcessor.processVoiceText(
        text: corrected,
        formType: context.originScreen,
      );

      if (extractedData.isNotEmpty) {
        VLOG('intent', 'AI extracted data: ${extractedData.keys.join(', ')}');

        // Clean temporary files after successful processing
        try {
          await _recordAndTranscribeUseCase.recordingRepository
              .cleanupTempFiles();
        } catch (_) {}

        // Increment AI voice usage counter (fire & forget)
        try {
          final user = _ref.read(authProvider).asData?.value;
          if (user != null && user.id != null) {
            _ref.read(aiUsageRepositoryProvider).incrementVoice(user.id!);
          }
        } catch (_) {}

        return VoiceResult(
          success: true,
          transcription: transcription,
          extractedData: extractedData,
        );
      }

      return VoiceResult(
        success: false,
        transcription: transcription,
        error: 'Could not understand the command',
      );
    } catch (e) {
      VLOG('ui', 'Error processing voice command: $e');
      _currentRecordingId = null;
      return VoiceResult(
        success: false,
        error: e.toString(),
      );
    } finally {
      // Always attempt to clean temporary files to avoid storage leaks
      try {
        await _recordAndTranscribeUseCase.recordingRepository
            .cleanupTempFiles();
      } catch (_) {}
    }
  }

  /// Cancel current recording
  Future<void> cancelRecording() async {
    if (_currentRecordingId != null) {
      try {
        await _recordAndTranscribeUseCase.cancelRecording(_currentRecordingId!);
        VLOG('record', 'Cancelled recording $_currentRecordingId');
      } catch (e) {
        VLOG('record', 'Error cancelling recording: $e');
      } finally {
        _currentRecordingId = null;
      }
    }
  }

  /// Check if currently recording
  bool get isRecording => _currentRecordingId != null;

  /// Dispose resources
  Future<void> dispose() async {
    if (_currentRecordingId != null) {
      await cancelRecording();
    }
    await _recordAndTranscribeUseCase.dispose();
  }
}

/// Provider for the voice command service
final voiceCommandServiceProvider = Provider<VoiceCommandService>((ref) {
  final recordAndTranscribeUseCase =
      ref.watch(recordAndTranscribeUseCaseProvider);
  final aiProcessor = ref.watch(aiVoiceProcessorProvider);
  final apiKeyService = ref.watch(core_keys.apiKeyServiceProvider);

  return VoiceCommandService(
    recordAndTranscribeUseCase: recordAndTranscribeUseCase,
    aiProcessor: aiProcessor,
    apiKeyService: apiKeyService,
    ref: ref,
  );
});

/// Voice logging helper
void VLOG(String phase, String message) {
  AppLogger.info('[VOICE][$phase] $message');
}
