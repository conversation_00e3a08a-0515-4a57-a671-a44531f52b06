import 'dart:convert';
import 'dart:developer' as dev;
import 'package:http/http.dart' as http;
import 'package:dart_openai/dart_openai.dart';
import '../../../core/services/api_key_service.dart';
import '../models/voice_form_type.dart';
import '../../../../core/utils/logger.dart';

/// AI-powered voice processor using OpenAI for intelligent text extraction
class AIVoiceProcessor {
  final ApiKeyService _apiKeyService;

  AIVoiceProcessor(this._apiKeyService) {
    final apiKey = _apiKeyService.getOpenAIApiKey();
    if (apiKey.isEmpty) {
      throw Exception('OpenAI API key not found');
    }
    OpenAI.apiKey = apiKey;
  }

  /// Process voice text using AI to extract structured data
  Future<Map<String, dynamic>> processVoiceText({
    required String text,
    required VoiceFormType formType,
  }) async {
    final prompt = _createPrompt(text, formType);
    AppLogger.info('AIVoiceProcessor: Sending prompt to OpenAI...');

    try {
      final chatCompletion = await OpenAI.instance.chat.create(
        model: "gpt-4o-mini",
        messages: [
          OpenAIChatCompletionChoiceMessageModel(
            content: [
              OpenAIChatCompletionChoiceMessageContentItemModel.text(
                'You are an intelligent assistant that extracts structured data from natural language text and returns it as a valid JSON object. Do not include any explanatory text, markdown formatting, or anything outside of the single JSON object.',
              ),
            ],
            role: OpenAIChatMessageRole.system,
          ),
          OpenAIChatCompletionChoiceMessageModel(
            content: [
              OpenAIChatCompletionChoiceMessageContentItemModel.text(prompt),
            ],
            role: OpenAIChatMessageRole.user,
          ),
        ],
        temperature: 0.1,
        maxTokens: 512,
      );

      final content = chatCompletion.choices.first.message.content?.first.text;
      AppLogger.info(
          'AIVoiceProcessor: Received response from OpenAI: $content');

      if (content != null) {
        try {
          final Map<String, dynamic> parsed =
              json.decode(content) as Map<String, dynamic>;
          // If OpenAI returned an empty object, fall back.
          if (parsed.isEmpty) {
            return _fallbackProcessing(text, formType);
          }
          return parsed;
        } catch (e) {
          AppLogger.error(
              'AIVoiceProcessor: Failed to decode JSON response from OpenAI',
              e);
          return _fallbackProcessing(text, formType);
        }
      }

      return _fallbackProcessing(text, formType);
    } catch (e, stack) {
      AppLogger.error(
          'AIVoiceProcessor: Failed to process text with OpenAI', e, stack);
      return {};
    }
  }

  /// Create AI prompt based on form type
  String _createPrompt(String text, VoiceFormType formType) {
    switch (formType) {
      case VoiceFormType.oilChange:
        return _createOilChangePrompt(text);
      case VoiceFormType.maintenance:
        return _createMaintenancePrompt(text);
    }
  }

  /// Create prompt for oil change data extraction
  String _createOilChangePrompt(String text) {
    return """
You are an AI assistant that extracts structured data from voice input about car oil changes.

Extract relevant information from this text: "$text"

Return ONLY a valid JSON object with these possible fields (omit fields if not mentioned):
{
  "current_mileage": number (current car mileage),
  "oil_brand_and_type": string (e.g., "Synthetic 5W-30", "Conventional"),
  "oil_endurance_km": number (the distance the oil is rated for, e.g., 3000, 5000, 10000),
  "was_filter_changed": boolean (true if the oil filter was changed),
  "oil_filter_type": string (type or brand of oil filter if mentioned),
  "cost_of_oil": number (cost of oil only),
  "cost_of_filter": number (cost of filter only),
  "total_cost": number (total cost if mentioned, overrides other costs),
  "service_provider_name": string (where the service was done),
  "additional_notes": string (any additional notes or comments),
  "service_date": string (ISO-8601 date, e.g., "${DateTime.now().toIso8601String().split('T')[0]}" for today. Infer from phrases like "yesterday", "last week" relative to the current date.)
}

Rules:
- Be precise with field names. For example, use "cost_of_oil" for oil cost and "cost_of_filter" for filter cost.
- If the user mentions a number like '5000' or '10000', it most likely refers to the "oil_endurance_km".
- Extract all costs as numbers without currency symbols.
- If a total cost is mentioned, prioritize it in the "total_cost" field.
- Handle both English and Arabic text.
- If no relevant information is found, return an empty JSON object {}.

Examples:
Input: "Changed synthetic oil for 10000 km at 45000 miles. It cost 60 dollars."
Output: {"current_mileage": 45000, "oil_brand_and_type": "Synthetic", "oil_endurance_km": 10000, "total_cost": 60}

Input: "Oil change yesterday, conventional oil for 5000, new filter, total was 45 bucks"
Output: {"oil_brand_and_type": "Conventional", "oil_endurance_km": 5000, "was_filter_changed": true, "total_cost": 45, "service_date": "${DateTime.now().subtract(const Duration(days: 1)).toIso8601String().split('T')[0]}"}

Input: "get me a new oil change for my car with 50,000 km, oil is synthetic 5w-30 for 5000 km, I paid 300 for oil and 50 for filter"
Output: {"current_mileage": 50000, "oil_brand_and_type": "synthetic 5w-30", "oil_endurance_km": 5000, "cost_of_oil": 300, "cost_of_filter": 50}
""";
  }

  /// Create prompt for maintenance data extraction
  String _createMaintenancePrompt(String text) {
    return """
You are an AI assistant that extracts structured data from voice input about car maintenance.

Extract relevant information from this text: "$text"

Return ONLY a valid JSON object with these possible fields (omit fields if not mentioned):
{
  "mileage": number (current car mileage),
  "maintenanceType": string (one of: generalService, brakeService, engineService, transmissionService, tireService, batteryService, airConditioning, electricalSystem, suspension, exhaustSystem, fuelSystem, coolingSystem, regularMaintenance, other),
  "description": string (detailed description of work done),
  "cost": number (total cost as a number),
  "serviceProvider": string (where the service was done),
  "notes": string (any additional notes),
  "date": string (ISO-8601 date if mentioned, e.g., "${DateTime.now().toIso8601String().split('T')[0]}" for today. Infer from relative terms like "yesterday", "last week" relative to today: ${DateTime.now().toIso8601String().split('T')[0]}),
  "carId": string (The user might mention the car by make, model, or nickname. For example, 'for my Honda', 'for the civic', 'for my truck'.)
}

Rules:
- Map maintenance descriptions to appropriate `maintenanceType` values:
  - "brake", "brakes" -> "brakeService"
  - "engine", "motor" -> "engineService"
  - "transmission", "gearbox" -> "transmissionService"
  - "tire", "tires", "wheel" -> "tireService"
  - "battery" -> "batteryService"
  - "ac", "air conditioning" -> "airConditioning"
  - "electrical" -> "electricalSystem"
  - "suspension", "shocks" -> "suspension"
  - "exhaust" -> "exhaustSystem"
  - "fuel" -> "fuelSystem"
  - "cooling", "radiator" -> "coolingSystem"
  - "checkup", "inspection" -> "regularMaintenance"
  - If it's none of the above, use "other".
- Extract costs as numbers without currency symbols.
- The `description` field should contain the original text related to the service.
- Handle both English and Arabic text.
- If no relevant information is found, return an empty JSON object {}.

Examples:
Input: "Brake service for my Honda at AutoZone cost 150 dollars at 30000 miles"
Output: {"carId": "Honda", "mileage": 30000, "maintenanceType": "brakeService", "description": "Brake service at AutoZone", "cost": 150, "serviceProvider": "AutoZone"}

Input: "Battery replacement last month for the civic, new battery installed, cost 120"
Output: {"carId": "civic", "maintenanceType": "batteryService", "description": "Battery replacement, new battery installed", "cost": 120, "date": "${DateTime.now().subtract(const Duration(days: 30)).toIso8601String().split('T')[0]}"}

Input: "yesterday i changed the tires on my truck for 500 dollars. it has 90000 km."
Output: {"carId": "truck", "date": "${DateTime.now().subtract(const Duration(days: 1)).toIso8601String().split('T')[0]}", "maintenanceType": "tireService", "cost": 500, "mileage": 90000, "description": "changed the tires"}

Input: "غيرت زيت الفرامل لسيارتي الشيري اريزو 5 امبارح عند الكيلو 60 الف كيلو متر و كلفتني 500 جنيه"
Output: {"carId": "شيري اريزو 5", "date": "${DateTime.now().subtract(const Duration(days: 1)).toIso8601String().split('T')[0]}", "maintenanceType": "brakeService", "cost": 500, "mileage": 60000, "description": "غيرت زيت الفرامل"}
""";
  }

  /// Fallback to rule-based processing if AI fails
  Map<String, dynamic> _fallbackProcessing(
      String text, VoiceFormType formType) {
    dev.log('AIVoiceProcessor: Using fallback rule-based processing');

    final data = <String, dynamic>{};
    final normalizedText = text.toLowerCase().trim();

    // Basic mileage extraction (works for both types)
    final mileageRegex =
        RegExp(r'(\d+)\s*(miles|mile|km|kilometers|كم|كيلومتر)');
    final mileageMatch = mileageRegex.firstMatch(normalizedText);
    if (mileageMatch != null) {
      data['mileage'] = int.tryParse(mileageMatch.group(1) ?? '');
    }

    // Basic cost extraction
    final costRegex = RegExp(r'(\d+(?:\.\d+)?)\s*(dollars?|euros?|دولار|\$)');
    final costMatch = costRegex.firstMatch(normalizedText);
    if (costMatch != null) {
      final cost = costMatch.group(1);
      if (formType == VoiceFormType.oilChange) {
        data['totalCost'] = cost;
      } else {
        data['cost'] = cost;
      }
    }

    if (formType == VoiceFormType.oilChange) {
      // Oil type extraction
      if (normalizedText.contains('synthetic') ||
          normalizedText.contains('سينتيك')) {
        data['oilType'] = 'Synthetic';
      } else if (normalizedText.contains('conventional') ||
          normalizedText.contains('تقليدي')) {
        data['oilType'] = 'Conventional';
      }

      // Oil quantity extraction
      final quantityRegex =
          RegExp(r'(\d+)\s*(liters|litre|gallons|quarts|لتر)');
      final quantityMatch = quantityRegex.firstMatch(normalizedText);
      if (quantityMatch != null) {
        data['oilQuantity'] =
            '${quantityMatch.group(1)} ${quantityMatch.group(2)}';
      }

      // Filter detection
      if (normalizedText.contains('filter') ||
          normalizedText.contains('فلتر')) {
        data['filterChanged'] = true;
        data['filterType'] = 'Standard Filter';
      }
    } else {
      // Maintenance type extraction
      if (normalizedText.contains('brake') ||
          normalizedText.contains('فرامل')) {
        data['maintenanceType'] = 'brakeService';
      } else if (normalizedText.contains('tire') ||
          normalizedText.contains('إطار')) {
        data['maintenanceType'] = 'tireService';
      } else if (normalizedText.contains('engine') ||
          normalizedText.contains('محرك')) {
        data['maintenanceType'] = 'engineService';
      } else if (normalizedText.contains('battery') ||
          normalizedText.contains('بطارية')) {
        data['maintenanceType'] = 'batteryService';
      } else if (normalizedText.contains('maintenance') ||
          normalizedText.contains('صيانة') ||
          normalizedText.contains('صيانه')) {
        data['maintenanceType'] = 'generalService';
      }

      // Service provider extraction
      final serviceRegex = RegExp(r'at\s+([a-zA-Z\s]+)(?:\s|$)');
      final serviceMatch = serviceRegex.firstMatch(normalizedText);
      if (serviceMatch != null) {
        data['serviceProvider'] = serviceMatch.group(1)?.trim();
      }

      data['description'] = text;
    }

    return data;
  }
}
