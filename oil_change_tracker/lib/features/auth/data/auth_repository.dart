import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:oil_change_tracker/core/models/user_model.dart';
import 'package:oil_change_tracker/features/auth/data/repositories/user_repository.dart';
import 'dart:developer' as dev;
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:oil_change_tracker/core/config/firebase_options.dart';

final authRepositoryProvider = Provider<AuthRepository>((ref) {
  final userRepository = ref.watch(userRepositoryProvider);
  return AuthRepository(
    userRepository,
    FirebaseAuth.instance,
    GoogleSignIn(),
  );
});

class AuthRepository {
  final UserRepository _userRepository;
  final FirebaseAuth _auth;
  final GoogleSignIn _googleSignIn;

  AuthRepository(
    this._userRepository, 
    this._auth,
    this._googleSignIn,
  );

  // Factory constructor for regular use
  factory AuthRepository.standard(UserRepository userRepository) {
    return AuthRepository(
      userRepository,
      FirebaseAuth.instance,
      GoogleSignIn(),
    );
  }

  Future<UserModel> signUp(String email, String password) async {
    final userCredential = await _auth.createUserWithEmailAndPassword(
      email: email,
      password: password,
    );

    final user = userCredential.user!;
    final userModel = UserModel(
      id: user.uid,
      email: user.email!,
      displayName: user.displayName ?? user.email!.split('@')[0],
      photoUrl: user.photoURL,
      isEmailVerified: user.emailVerified,
    );

    await _userRepository.createUser(userModel);
    return userModel;
  }

  Future<UserModel> signIn(String email, String password) async {
    final userCredential = await _auth.signInWithEmailAndPassword(
      email: email,
      password: password,
    );

    final user = userCredential.user!;
    final userModel = UserModel(
      id: user.uid,
      email: user.email!,
      displayName: user.displayName ?? user.email!.split('@')[0],
      photoUrl: user.photoURL,
      isEmailVerified: user.emailVerified,
    );

    return userModel;
  }

  Future<UserModel> signInWithGoogle() async {
    try {
      dev.log('Starting Google Sign In process');
      
      // Configure Google Sign In with web client ID
      await _googleSignIn.signOut(); // Clear any existing sign in state
      dev.log('Cleared existing Google Sign In state');
      
      // Trigger the authentication flow with explicit scopes
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      
      if (googleUser == null) {
        dev.log('Google Sign In was cancelled by user');
        throw Exception('Google Sign In was cancelled');
      }
      
      dev.log('Google Sign In successful: ${googleUser.email}');
      dev.log('Account details - ID: ${googleUser.id}, Display Name: ${googleUser.displayName}');

      // Obtain the auth details from the request
      dev.log('Getting Google auth details');
      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;
      
      if (googleAuth.accessToken == null || googleAuth.idToken == null) {
        dev.log('Failed to get Google auth tokens');
        throw Exception('Failed to get Google auth tokens');
      }
      
      dev.log('Got Google auth tokens - Access Token length: ${googleAuth.accessToken!.length}');

      // Create a new credential
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );
      dev.log('Created Firebase credential');

      // Sign in to Firebase with the credential
      dev.log('Attempting to sign in to Firebase');
      try {
        final userCredential = await _auth.signInWithCredential(credential);
        final user = userCredential.user;

        if (user == null) {
          dev.log('Firebase user is null after sign in');
          throw Exception('Failed to sign in with Firebase');
        }
        
        dev.log('Firebase sign in successful: ${user.email}');
        dev.log('Firebase user details - UID: ${user.uid}, Email Verified: ${user.emailVerified}');

        final userModel = UserModel(
          id: user.uid,
          email: user.email!,
          displayName: user.displayName ?? user.email!.split('@')[0],
          photoUrl: user.photoURL,
          isEmailVerified: user.emailVerified,
        );

        // Create or update user in Firestore
        dev.log('Creating/updating user in Firestore');
        await _userRepository.createUser(userModel);
        dev.log('Google Sign In process completed successfully');
        return userModel;
      } on FirebaseAuthException catch (e) {
        dev.log('Firebase Auth Exception: ${e.code} - ${e.message}', error: e);
        throw Exception('Firebase Auth Error: ${e.message}');
      }
    } catch (e) {
      dev.log('Error during Google Sign In: ${e.toString()}', error: e);
      rethrow;
    }
  }

  Future<void> signOut() async {
    await _auth.signOut();
  }

  Future<UserModel?> getCurrentUser() async {
    final user = _auth.currentUser;
    if (user == null) return null;

    return _userRepository.getCurrentUser();
  }

  Future<void> updateUserProfile(UserModel user) async {
    try {
      final token = await _auth.currentUser?.getIdToken();
      if (token == null) throw Exception('User not authenticated');
      
      dev.log('Making request to update user profile');
      
      final response = await http.post(
        Uri.parse('https://identitytoolkit.googleapis.com/v1/accounts:update?key=${DefaultFirebaseOptions.android.apiKey}'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode(user.toJson()),
      );
      
      dev.log('Profile update response status: ${response.statusCode}');
      
      if (response.statusCode != 200) {
        final error = jsonDecode(response.body)['error'];
        dev.log('Profile update error: $error');
        throw Exception('Failed to update profile: $error');
      }
      
      // Update user display name in Firebase Auth
      if (user.displayName.isNotEmpty) {
        await _auth.currentUser?.updateDisplayName(user.displayName);
      }
      
      dev.log('Profile update successful');
      
      // Update the user data in Firestore
      await _updateFirestoreUserData(user);
      
    } catch (e) {
      throw Exception('Error updating profile: $e');
    }
  }
  
  Future<void> _updateFirestoreUserData(UserModel user) async {
    try {
      final userId = _auth.currentUser?.uid;
      if (userId == null) throw Exception('User not authenticated');
      
      dev.log('Updating user data in Firestore');
      await _userRepository.updateUser(user);
      dev.log('Firestore update successful');
    } catch (e) {
      dev.log('Error in updateUserProfile: $e');
      throw Exception('Error updating user data in Firestore: $e');
    }
  }
} 