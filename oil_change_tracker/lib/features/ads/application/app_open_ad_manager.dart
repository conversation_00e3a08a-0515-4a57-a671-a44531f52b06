import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:oil_change_tracker/core/utils/logger.dart';
import '../models/ad_configuration.dart';
import 'dart:async';
import 'dart:math';
import 'package:oil_change_tracker/features/subscription/services/subscription_service.dart';

/// Manages loading and showing App Open ads.
class AppOpenAdManager with WidgetsBindingObserver {
  AppOpenAd? _appOpenAd;
  bool _isShowingAd = false;
  bool _isInitialized = false;
  String? _lastErrorMessage;
  int _loadAttempts = 0;
  static const int _maxLoadAttempts =
      2; // Reduced to avoid too many connection errors

  // Backoff strategy for retries
  int _retryDelaySeconds = 5;
  Timer? _retryTimer;
  bool _isAttemptingToLoad = false;

  // Keep track of network availability
  bool _hasNetworkError = false;

  // Controls whether ads should be shown on app resume
  // Initially false, only set to true after login
  bool _showAdsAfterLogin = false;

  // Subscription service to check premium status
  final SubscriptionService _subscriptionService;

  // Test mode detection
  static final bool _inTestMode = kDebugMode;

  // Ad unit ID from configuration
  String get _adUnitId => AdConfiguration.appOpenAdUnitId;

  // Keep track of the last time an ad was shown
  DateTime? _lastAdShowTime;
  final Duration _minInterval =
      const Duration(minutes: 1); // 1 minute minimum interval between ads

  bool get isAdAvailable => _appOpenAd != null;
  String? get lastError => _lastErrorMessage;
  bool get isInitialized => _isInitialized;

  // Constructor with subscription service
  AppOpenAdManager(this._subscriptionService);

  // Setter to enable showing ads after successful login
  set showAdsAfterLogin(bool value) {
    _showAdsAfterLogin = value;
    AppLogger.info('AppOpenAdManager: Showing ads after login set to $value');

    if (value) {
      // Clear any existing retry timers
      _retryTimer?.cancel();

      // Reset attempt counters to ensure we can try loading again
      _loadAttempts = 0;

      // Reset flags to allow new attempts
      _isAttemptingToLoad = false;
      _hasNetworkError = false;

      // Add a small delay to ensure app is ready for ads
      Future.delayed(const Duration(milliseconds: 500), () {
        if (isAdAvailable) {
          // Show ad immediately after login if available
          AppLogger.info(
              'AppOpenAdManager: Ad available after login, showing it');
          showAdIfAvailable();
        } else if (!_isAttemptingToLoad) {
          // Try to load an ad if we don't have one and not already loading
          AppLogger.info(
              'AppOpenAdManager: No ad available after login, loading one');
          _loadAd();
        }
      });
    }
  }

  bool get showAdsAfterLogin => _showAdsAfterLogin;

  void initialize() {
    AppLogger.info('AppOpenAdManager: Initializing...');
    try {
      WidgetsBinding.instance.addObserver(this);
      _isInitialized = true;

      // Set up Mobile Ads SDK with better error handling
      try {
        // Set up test device ids if in debug mode
        if (kDebugMode) {
          final RequestConfiguration config = RequestConfiguration(
            testDeviceIds: AdConfiguration.testDeviceIds,
            maxAdContentRating: MaxAdContentRating.pg,
            tagForChildDirectedTreatment:
                TagForChildDirectedTreatment.unspecified,
            tagForUnderAgeOfConsent: TagForUnderAgeOfConsent.unspecified,
          );
          MobileAds.instance.updateRequestConfiguration(config);
        }

        // Initialize SDK
        MobileAds.instance.initialize().then((_) {
          AppLogger.info('AppOpenAdManager: Mobile Ads SDK initialized');

          // Only preload the ad if showAdsAfterLogin is true
          // This prevents ads from loading until after login
          if (_showAdsAfterLogin) {
            // Use a microtask to avoid blocking the main thread
            // and increase delay to allow login to complete first
            Future.delayed(const Duration(seconds: 10), () {
              if (!_isAttemptingToLoad && _showAdsAfterLogin) {
                AppLogger.info(
                    'AppOpenAdManager: Delayed preloading of ad after login');
                _loadAd();
              }
            });
          } else {
            AppLogger.info(
                'AppOpenAdManager: Not preloading ad - user not logged in yet');
          }
        }).catchError((error) {
          _lastErrorMessage = 'Failed to initialize Mobile Ads SDK: $error';
          AppLogger.error('AppOpenAdManager: Mobile Ads SDK init error', error);
        });
      } catch (e) {
        AppLogger.error('AppOpenAdManager: MobileAds initialize error', e);
      }
    } catch (e) {
      _lastErrorMessage = 'Initialization error: $e';
      AppLogger.error('AppOpenAdManager: Initialization failed', e);
    }
  }

  Future<void> _loadAd() async {
    // Check for premium subscription first
    final hasSubscription = await _subscriptionService.hasActiveSubscription();
    if (hasSubscription) {
      AppLogger.info(
          'AppOpenAdManager: Skipping ad load - user has premium subscription');

      // Dispose any existing ad if user has premium
      if (_appOpenAd != null) {
        _appOpenAd!.dispose();
        _appOpenAd = null;
      }
      return;
    }

    // Already attempting to load
    if (_isAttemptingToLoad) {
      AppLogger.info('AppOpenAdManager: Already attempting to load an ad');
      return;
    }

    // Skip if not logged in
    if (!_showAdsAfterLogin) {
      AppLogger.info('AppOpenAdManager: Not loading ad - user not logged in');
      return;
    }

    // Don't attempt to load if exceeded max attempts
    if (_loadAttempts >= _maxLoadAttempts) {
      AppLogger.warning(
          'AppOpenAdManager: Maximum load attempts reached. Will retry later.');
      _scheduleRetry();
      return;
    }

    // Increase load attempt counter and set loading flag
    _loadAttempts++;
    _isAttemptingToLoad = true;

    // Use a microtask to avoid blocking the main thread
    Future.microtask(() async {
      try {
        AppLogger.info(
            'AppOpenAdManager: Loading App Open Ad (attempt $_loadAttempts of $_maxLoadAttempts)...');

        // Add timeout for ad loading
        Timer(const Duration(seconds: 20), () {
          if (_isAttemptingToLoad) {
            AppLogger.warning(
                'AppOpenAdManager: Ad load timed out after 20 seconds');
            _isAttemptingToLoad = false;
            _hasNetworkError = true;
            _scheduleRetry();
          }
        });

        AppOpenAd.load(
          adUnitId: _adUnitId,
          request: const AdRequest(),
          adLoadCallback: AppOpenAdLoadCallback(
            onAdLoaded: (ad) {
              AppLogger.info('AppOpenAdManager: Ad loaded successfully.');
              _appOpenAd = ad;
              _loadAttempts = 0; // Reset attempts on successful load
              _lastErrorMessage = null;
              _isAttemptingToLoad = false;
              _hasNetworkError = false;
              _retryDelaySeconds = 5; // Reset retry delay

              _appOpenAd!.fullScreenContentCallback = FullScreenContentCallback(
                onAdShowedFullScreenContent: (ad) {
                  _isShowingAd = true;
                  AppLogger.info('AppOpenAdManager: Ad showed full screen.');
                },
                onAdFailedToShowFullScreenContent: (ad, error) {
                  _lastErrorMessage = 'Failed to show ad: ${error.message}';
                  AppLogger.error(
                      'AppOpenAdManager: Failed to show full screen content: ${error.message}');
                  _isShowingAd = false;
                  ad.dispose();
                  _appOpenAd = null;

                  // Only try to load another ad if we're not exceeding our retry limits
                  if (_loadAttempts < _maxLoadAttempts) {
                    _loadAd();
                  } else {
                    _scheduleRetry();
                  }
                },
                onAdDismissedFullScreenContent: (ad) {
                  AppLogger.info('AppOpenAdManager: Ad dismissed full screen.');
                  _isShowingAd = false;
                  ad.dispose();
                  _appOpenAd = null;

                  // Load the next ad after a short delay
                  Future.delayed(const Duration(seconds: 1), () {
                    if (!_isAttemptingToLoad) {
                      _loadAd();
                    }
                  });
                },
              );
            },
            onAdFailedToLoad: (error) {
              _lastErrorMessage = 'Failed to load ad: ${error.message}';
              AppLogger.error(
                  'AppOpenAdManager: Failed to load ad: ${error.message} (Code: ${error.code})');
              _appOpenAd = null;
              _isAttemptingToLoad = false;

              // Check for network errors by code or message
              if (error.code == 2 || // LoadAdError.NETWORK_ERROR is 2
                  error.message.toLowerCase().contains('network') ||
                  error.message.toLowerCase().contains('connection')) {
                _hasNetworkError = true;
                AppLogger.warning(
                    'AppOpenAdManager: Network error detected, scheduling retry with longer delay');
                _scheduleRetry(increasedDelay: true);
              } else {
                // Implement retry logic with delay
                _scheduleRetry();
              }
            },
          ),
        );
      } catch (e) {
        _lastErrorMessage = 'Ad load exception: $e';
        AppLogger.error('AppOpenAdManager: Exception during ad load', e);
        _isAttemptingToLoad = false;
        _scheduleRetry();
      }
    });
  }

  void _scheduleRetry({bool increasedDelay = false}) {
    // Cancel any existing retry timer
    _retryTimer?.cancel();

    // Calculate delay - use longer delay for network errors
    final delay = increasedDelay ? _retryDelaySeconds * 2 : _retryDelaySeconds;

    // Cap delay at 5 minutes
    _retryDelaySeconds = min(delay, 300);

    AppLogger.info(
        'AppOpenAdManager: Scheduling ad load retry in $_retryDelaySeconds seconds');

    // Schedule retry
    _retryTimer = Timer(Duration(seconds: _retryDelaySeconds), () {
      AppLogger.info('AppOpenAdManager: Attempting ad load retry');
      _loadAttempts = 0; // Reset attempts for the new cycle
      _loadAd();
    });
  }

  Future<void> showAdIfAvailable() async {
    // Check for premium subscription first
    final hasSubscription = await _subscriptionService.hasActiveSubscription();
    if (hasSubscription) {
      AppLogger.info(
          'AppOpenAdManager: Skipping ad show - user has premium subscription');
      return;
    }

    // Only show ads if flag is enabled or explicitly called
    if (!isAdAvailable) {
      AppLogger.info('AppOpenAdManager: Tried to show ad, but not available.');
      if (!_isAttemptingToLoad) {
        _loadAd(); // Try loading again
      }
      return;
    }
    if (_isShowingAd) {
      AppLogger.info(
          'AppOpenAdManager: Tried to show ad, but already showing.');
      return;
    }

    // Check time interval
    final now = DateTime.now();
    if (_lastAdShowTime != null &&
        now.difference(_lastAdShowTime!) < _minInterval) {
      AppLogger.info(
          'AppOpenAdManager: Tried to show ad, but minimum interval not met.');
      return;
    }

    try {
      AppLogger.info('AppOpenAdManager: Showing ad...');
      _appOpenAd!.show();
      _lastAdShowTime = now;
    } catch (e) {
      _lastErrorMessage = 'Error showing ad: $e';
      AppLogger.error('AppOpenAdManager: Error showing ad', e);
      // Reset and try to load a new ad
      _appOpenAd?.dispose();
      _appOpenAd = null;
      if (!_isAttemptingToLoad) {
        _loadAd();
      }
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      AppLogger.info(
          'AppOpenAdManager: App resumed. Show ads flag: $_showAdsAfterLogin');
      // Only show ads on resume if the flag is set to true (user is logged in)
      if (_showAdsAfterLogin) {
        showAdIfAvailable();
      }
    }
  }

  void dispose() {
    AppLogger.info('AppOpenAdManager: Disposing...');
    WidgetsBinding.instance.removeObserver(this);
    _retryTimer?.cancel();
    _appOpenAd?.dispose();
    _appOpenAd = null;
    _isInitialized = false;
  }
}
